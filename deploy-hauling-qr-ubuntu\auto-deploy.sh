#!/bin/bash

# =============================================================================
# HAULING QR TRIP SYSTEM - CLEAN MODULAR DEPLOYMENT ORCHESTRATOR
# =============================================================================
# Version: 4.0.0 - Pure orchestrator with no duplicate functionality
# Compatible with: Ubuntu 24.04 LTS, 22.04 LTS, 20.04 LTS
# Description: Clean automated deployment orchestrator calling modular scripts
# =============================================================================

set -euo pipefail

# =============================================================================
# CONFIGURATION VARIABLES
# =============================================================================
readonly VERSION="5.0.0"
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly LOG_DIR="/var/log/hauling-qr-deployment"
readonly LOG_FILE="${LOG_DIR}/auto-deploy-$(date +%Y%m%d-%H%M%S).log"

# Load shared configuration FIRST (required by other modules)
readonly SHARED_CONFIG="${SCRIPT_DIR}/shared-config.sh"
if [[ -f "$SHARED_CONFIG" ]]; then
  source "$SHARED_CONFIG"
else
  echo "❌ ERROR: Shared configuration not found: $SHARED_CONFIG"
  exit 1
fi

# Load intelligent deployment framework
readonly INTELLIGENT_FRAMEWORK="${SCRIPT_DIR}/intelligent-deployment-framework.sh"
if [[ -f "$INTELLIGENT_FRAMEWORK" ]]; then
  source "$INTELLIGENT_FRAMEWORK"
else
  echo "❌ ERROR: Intelligent deployment framework not found: $INTELLIGENT_FRAMEWORK"
  exit 1
fi

# Load deployment markers system for idempotent deployments
readonly DEPLOYMENT_MARKERS="${SCRIPT_DIR}/deployment-markers.sh"
if [[ -f "$DEPLOYMENT_MARKERS" ]]; then
  source "$DEPLOYMENT_MARKERS"
else
  echo "❌ ERROR: Deployment markers system not found: $DEPLOYMENT_MARKERS"
  exit 1
fi
readonly GITHUB_REPO="mightybadz18/hauling-qr-trip-management"
readonly GITHUB_PAT="${GITHUB_PAT:-}"
readonly GITHUB_USERNAME="${GITHUB_USERNAME:-}"
# CRITICAL: Dynamic domain configuration - use environment variable with fallback
readonly PRODUCTION_DOMAIN="${PRODUCTION_DOMAIN:-truckhaul.top}"
readonly MANUAL_IP="${MANUAL_IP:-}"

# Validate PRODUCTION_DOMAIN is set
if [[ -z "$PRODUCTION_DOMAIN" ]]; then
  echo "❌ ERROR: PRODUCTION_DOMAIN environment variable is required"
  echo "   Example: export PRODUCTION_DOMAIN=truckhaul.local"
  echo "   Example: export PRODUCTION_DOMAIN=truckhaul.top"
  exit 1
fi

# Log domain configuration
echo "🌐 DOMAIN CONFIGURATION:"
echo "   PRODUCTION_DOMAIN: $PRODUCTION_DOMAIN"
echo "   API_BASE_URL: https://api.$PRODUCTION_DOMAIN"
echo "   FRONTEND_URL: https://$PRODUCTION_DOMAIN"

# Application Configuration
readonly APP_NAME="hauling-qr-system"
readonly APP_DIR="/var/www/${APP_NAME}"
readonly DB_NAME="hauling_qr_system"
readonly DB_USER="postgres"
readonly DB_PASSWORD="PostgreSQLPassword123"
readonly JWT_SECRET="hauling_qr_jwt_secret_2025_secure_key_for_production"

# Network Configuration - CLOUDFLARE COMPATIBLE
DETECTED_VPS_IP=""
readonly CLIENT_PORT=3000
readonly SERVER_HTTP_PORT=8080  # Cloudflare-compatible port
readonly SERVER_HTTPS_PORT=8443
readonly SERVER_PORT=8080

# Ubuntu User Configuration
readonly UBUNTU_USER="ubuntu"
readonly UBUNTU_HOME="/home/<USER>"

# Deployment Configuration
DEPLOYMENT_ENV=""
PRESERVE_ARTIFACTS=false

# Color codes for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================
# Create log directory with proper permissions
sudo mkdir -p "$LOG_DIR"
sudo chmod 755 "$LOG_DIR"
sudo chown ubuntu:ubuntu "$LOG_DIR" 2>/dev/null || true

ts() { date '+%Y-%m-%d %H:%M:%S'; }
log() { echo "[$(ts)] $*" | tee -a "$LOG_FILE"; }

log_info() {
  log "INFO  | $1"
}

log_success() {
  log "OK    | $1"
}

log_warning() {
  log "WARN  | $1"
}

log_error() {
  log "ERROR | $1"
}

log_debug() {
  log "DEBUG | $1"
}

# =============================================================================
# COMMAND LINE ARGUMENT PARSING
# =============================================================================
parse_arguments() {
  while [[ $# -gt 0 ]]; do
    case $1 in
      --environment=*)
        DEPLOYMENT_ENV="${1#*=}"
        log_info "Environment specified via command line: $DEPLOYMENT_ENV"
        shift
        ;;
      --preserve-artifacts)
        PRESERVE_ARTIFACTS=true
        log_info "Artifact preservation enabled"
        shift
        ;;
      --manual-ip=*)
        MANUAL_IP="${1#*=}"
        log_info "Manual IP specified: $MANUAL_IP"
        shift
        ;;
      --help)
        show_help
        exit 0
        ;;
      *)
        log_error "Unknown argument: $1"
        show_help
        exit 1
        ;;
    esac
  done
}

show_help() {
  cat << EOF
Hauling QR Trip System - Modular Deployment Script v${VERSION}

Usage: $0 [OPTIONS]

OPTIONS:
  --environment=ENV     Set deployment environment (development|production)
  --preserve-artifacts  Keep build artifacts and logs after deployment
  --manual-ip=IP        Manually specify VPS IP address
  --help               Show this help message

ENVIRONMENT VARIABLES:
  GITHUB_PAT           GitHub Personal Access Token (required for private repos)
  GITHUB_USERNAME      GitHub username (optional)

EXAMPLES:
  sudo -E ./auto-deploy.sh
  sudo -E ./auto-deploy.sh --environment=production
  sudo -E ./auto-deploy.sh --manual-ip=*************

EOF
}

# =============================================================================
# INTELLIGENT MODULAR SCRIPT EXECUTION
# =============================================================================
execute_modular_script() {
  local script_name="$1"
  local component_type="${2:-optional}"  # essential|optional
  local script_path="${SCRIPT_DIR}/${script_name}"

  # OPTIMIZED: Reduced timeout configurations for sub-15-minute deployment
  local timeout_minutes=10  # Reduced default timeout from 30 to 10 minutes
  local max_retries=3       # Default retries

  case "$script_name" in
    "1_install-system-dependencies.sh")
      timeout_minutes=8     # Reduced from 15 to 8 minutes with parallel processing
      max_retries=3         # Reduced from 5 to 3 retries
      component_type="essential"
      ;;
    "4_install-postgresql.sh")
      timeout_minutes=10    # Reduced from 40 to 10 minutes
      max_retries=2         # Reduced from 3 to 2 retries
      component_type="essential"
      ;;
    "5_install-nginx.sh")
      timeout_minutes=5     # Reduced from 20 to 5 minutes
      max_retries=2         # Reduced from 3 to 2 retries
      component_type="essential"
      ;;
    "3_build-application.sh")
      timeout_minutes=8     # Reduced from 35 to 8 minutes
      max_retries=2         # Keep at 2 retries
      component_type="essential"
      ;;
    "6_install-pm2.sh")
      timeout_minutes=5     # Reduced from 15 to 5 minutes
      max_retries=2         # Reduced from 3 to 2 retries
      component_type="optional"
      ;;
    "2_setup-repository-environment.sh")
      timeout_minutes=5     # Reduced from 25 to 5 minutes
      max_retries=2         # Reduced from 3 to 2 retries
      component_type="essential"
      ;;
  esac

  local timeout_seconds=$((timeout_minutes * 60))
  local component_name="${script_name%.*}"  # Remove .sh extension

  log_info "🔧 Preparing intelligent execution: $script_name"
  log_info "📁 Script path: $script_path"
  log_info "🎯 Component type: $component_type"
  log_info "⏱️ Timeout: $timeout_minutes minutes (${timeout_seconds}s)"
  log_info "🔄 Max retries: $max_retries"

  # Check if script exists
  if [[ ! -f "$script_path" ]]; then
    log_error "❌ Modular script not found: $script_path"
    update_deployment_progress "$component_name" "failed" "$component_type" "Script not found"
    return 1
  fi

  # Make script executable
  chmod +x "$script_path" 2>/dev/null || true

  # Initialize progress tracking for this component
  init_intelligent_progress_tracking "$component_name" "$component_type"

  # ENHANCED: Start performance timing
  start_component_timing "$component_name"

  # Execute script with intelligent retry wrapper
  log_info "🚀 Starting intelligent execution of $script_name..."

  # Define the command to execute
  local script_command=("bash" "$script_path")

  # Use intelligent retry wrapper
  if intelligent_retry_wrapper "$component_name" "$component_type" "$max_retries" "$timeout_seconds" "${script_command[@]}"; then
    # ENHANCED: End timing on success
    end_component_timing "$component_name" "completed"
    log_success "✅ $script_name completed successfully with intelligent deployment system"
    return 0
  else
    local exit_code=$?

    # ENHANCED: End timing on failure
    end_component_timing "$component_name" "failed"

    if [[ $exit_code -eq 1 ]]; then
      log_error "❌ Essential component $script_name failed - deployment cannot continue"
      return 1
    elif [[ $exit_code -eq 2 ]]; then
      log_warning "⚠️ Optional component $script_name failed - continuing deployment"
      return 0  # Continue deployment for optional components
    else
      log_error "❌ $script_name failed with unexpected exit code $exit_code"
      return $exit_code
    fi
  fi
}

# =============================================================================
# ENVIRONMENT DETECTION
# =============================================================================
detect_environment() {
  log_info "🔍 Detecting deployment environment..."

  # Auto-detect environment if not specified
  if [[ -z "$DEPLOYMENT_ENV" ]]; then
    # Default to production for VPS deployments
    # Check if we're in a VPS environment (not WSL testing)
    if [[ -f "/etc/hostname" ]] && grep -q "vps\|server\|ubuntu" /etc/hostname 2>/dev/null; then
      DEPLOYMENT_ENV="production"
      log_info "🏭 VPS environment detected - using production mode"
    elif [[ -n "$PRODUCTION_DOMAIN" ]] && [[ "$DETECTED_VPS_IP" != "" ]]; then
      DEPLOYMENT_ENV="production"
      log_info "🌐 Production domain detected - using production mode"
    else
      # For WSL testing, still use production mode unless explicitly set to development
      DEPLOYMENT_ENV="production"
      log_info "🧪 Default environment - using production mode"
    fi
  fi

  log_info "🏗️ Deployment environment: $DEPLOYMENT_ENV"
  
  # Export environment variables for modular scripts
  export DEPLOYMENT_ENV
  export DETECTED_VPS_IP
  export GITHUB_PAT
  export GITHUB_USERNAME
  export GITHUB_REPO
  export APP_DIR
  export DB_NAME
  export DB_USER
  export DB_PASSWORD
  export JWT_SECRET
  export SERVER_HTTP_PORT
  export CLIENT_PORT
  export UBUNTU_USER
  export PRODUCTION_DOMAIN
  export MANUAL_IP
}

# =============================================================================
# SERVICE VERIFICATION (for Phase 9)
# =============================================================================
verify_service_status() {
  local service_name="$1"
  local check_method="${2:-systemctl}"
  
  case "$check_method" in
    "systemctl")
      systemctl is-active "$service_name" >/dev/null 2>&1
      ;;
    "process")
      pgrep "$service_name" >/dev/null 2>&1
      ;;
    "port")
      local port="$3"
      netstat -tuln | grep ":$port " >/dev/null 2>&1
      ;;
    *)
      log_error "Unknown service check method: $check_method"
      return 1
      ;;
  esac
}

start_service_with_retry() {
  local service_name="$1"
  local method="${2:-systemctl}"
  local max_attempts="${3:-3}"
  local delay="${4:-5}"
  
  for ((i=1; i<=max_attempts; i++)); do
    log_info "Starting $service_name (attempt $i/$max_attempts)..."
    
    case "$method" in
      "systemctl")
        if systemctl start "$service_name" >/dev/null 2>&1; then
          sleep "$delay"
          if verify_service_status "$service_name" "systemctl"; then
            log_success "✅ $service_name started successfully"
            return 0
          fi
        fi
        ;;
      "service")
        if service "$service_name" start >/dev/null 2>&1; then
          sleep "$delay"
          if verify_service_status "$service_name" "process"; then
            log_success "✅ $service_name started successfully"
            return 0
          fi
        fi
        ;;
    esac
    
    if [[ $i -lt $max_attempts ]]; then
      log_warning "⚠️ $service_name start attempt $i failed, retrying in ${delay}s..."
      sleep "$delay"
    fi
  done
  
  log_error "❌ Failed to start $service_name after $max_attempts attempts"
  return 1
}

# =============================================================================
# MAIN DEPLOYMENT EXECUTION
# =============================================================================
main() {
  log_info "🚀 Starting Intelligent Hauling QR Trip System Deployment"
  log_info "📅 Deployment started at: $(date)"
  log_info "🏗️ Version: $VERSION (Intelligent Modular Orchestrator)"
  log_info "📝 Log file: $LOG_FILE"
  log_info "🧠 Intelligent deployment framework: ENABLED"

  # Parse command line arguments
  parse_arguments "$@"

  # Detect and setup environment
  detect_environment

  # Initialize intelligent deployment tracking
  log_info "🔧 Initializing intelligent deployment system..."
  init_intelligent_progress_tracking "deployment-orchestrator" "essential"

  # ENHANCED: Initialize health monitoring and performance tracking
  init_deployment_health_monitoring "deploy-$(date +%Y%m%d-%H%M%S)"
  init_performance_monitoring

  # Update deployment state
  python3 -c "
import json
with open('$DEPLOYMENT_STATE_FILE', 'r') as f:
    state = json.load(f)
state['phase'] = 'deployment'
state['status'] = 'in_progress'
with open('$DEPLOYMENT_STATE_FILE', 'w') as f:
    json.dump(state, f, indent=2)
" 2>/dev/null || true

  log_success "✅ Intelligent deployment system initialized"

  # INTELLIGENT DEPLOYMENT PIPELINE WITH OPTIMIZATION COORDINATION
  log_info "🧠 Starting Intelligent Deployment Pipeline with Optimization Coordination"
  start_performance_timer "deployment-total"

  # Initialize intelligent deployment framework
  init_markers_system

  log_info "🏗️ Phase 0: System Resource Optimization (4 vCPU / 8GB RAM) - Intelligent"
  create_phase_marker "phase-0-system-optimization" "started"
  start_performance_timer "phase-0"

  if ! execute_modular_script "0_optimize-system-resources.sh" "essential"; then
    create_phase_marker "phase-0-system-optimization" "failed"
    update_deployment_health "phase-0" "error" "System resource optimization failed"
    log_error "❌ System resource optimization failed"
    show_deployment_status "failed"
    exit 1
  fi

  end_performance_timer "phase-0"
  create_phase_marker "phase-0-system-optimization" "completed"

  log_info "🏗️ Phase 1: System Dependencies Installation (Node.js v20.x LTS) - Intelligent"
  create_phase_marker "phase-1-system-dependencies" "started"
  start_performance_timer "phase-1"

  if ! execute_modular_script "1_install-system-dependencies.sh" "essential"; then
    create_phase_marker "phase-1-system-dependencies" "failed"
    update_deployment_health "phase-1" "error" "System dependencies installation failed"
    log_error "❌ System dependencies installation failed"
    show_deployment_status "failed"
    exit 1
  fi

  end_performance_timer "phase-1"
  create_phase_marker "phase-1-system-dependencies" "completed"

  log_info "🏗️ Phase 2: Repository and Environment Setup - Intelligent"
  start_performance_timer "phase-2"

  if ! execute_modular_script "2_setup-repository-environment.sh" "essential"; then
    log_error "❌ Repository and environment setup failed"
    show_deployment_status "failed"
    exit 1
  fi

  end_performance_timer "phase-2"

  log_info "🏗️ Phase 3: Application Building - Intelligent"
  start_performance_timer "phase-3"

  if ! execute_modular_script "3_build-application.sh" "essential"; then
    log_error "❌ Application building failed"
    show_deployment_status "failed"
    exit 1
  fi

  end_performance_timer "phase-3"

  log_info "🏗️ Phase 4: Database Installation - Intelligent"
  start_performance_timer "phase-4"

  if ! execute_modular_script "4_install-postgresql.sh" "essential"; then
    log_error "❌ PostgreSQL installation failed"
    show_deployment_status "failed"
    exit 1
  fi

  end_performance_timer "phase-4"

  log_info "🏗️ Phase 5: Web Server Installation - Intelligent"
  start_performance_timer "phase-5"

  if ! execute_modular_script "5_install-nginx.sh" "essential"; then
    log_error "❌ Nginx installation failed"
    show_deployment_status "failed"
    exit 1
  fi

  end_performance_timer "phase-5"

  log_info "🏗️ Phase 6: Process Manager Installation - Intelligent"
  start_performance_timer "phase-6"

  if ! execute_modular_script "6_install-pm2.sh" "optional"; then
    log_warning "⚠️ PM2 installation failed, but deployment continues"
  fi

  end_performance_timer "phase-6"

  log_info "🏗️ Phase 7: Permission Management"
  if ! execute_modular_script "7_fix-permissions-ubuntu-user.sh" "optional"; then
    log_warning "⚠️ Permission management failed, but deployment continues"
  fi

  log_info "🏗️ Phase 8: Cleanup Operations"
  if ! execute_modular_script "8_cleanup-deployment.sh" "optional"; then
    log_warning "⚠️ Cleanup operations failed, but deployment continues"
  fi

  log_info "🏗️ Phase 8.5: System Startup Configuration (CRITICAL for CORS persistence)"
  if ! execute_modular_script "setup-system-startup.sh" "essential"; then
    log_error "❌ System startup configuration failed - CORS may not persist after reboot"
    show_deployment_status "failed"
    exit 1
  fi

  log_info "🏗️ Phase 9: System Optimization Validation"
  # Enhanced validation script detection with multiple path checks
  local validation_script_paths=(
    "${SCRIPT_DIR}/9_validate-system-optimization.sh"
    "$(dirname "$0")/9_validate-system-optimization.sh"
    "/home/<USER>/hauling-qr-trip-management/deploy-hauling-qr-ubuntu/9_validate-system-optimization.sh"
  )

  local validation_script_found=false
  for script_path in "${validation_script_paths[@]}"; do
    if [[ -f "$script_path" && -x "$script_path" ]]; then
      log_info "✅ Found validation script at: $script_path"
      if ! execute_modular_script "9_validate-system-optimization.sh" "optional"; then
        log_warning "⚠️ System optimization validation failed, but deployment continues"
      fi
      validation_script_found=true
      break
    fi
  done

  if [[ "$validation_script_found" == false ]]; then
    log_warning "⚠️ System optimization validation script not found in any expected location - skipping (deployment continues)"
    log_info "📍 Searched paths:"
    for script_path in "${validation_script_paths[@]}"; do
      log_info "   - $script_path"
    done
  fi

  # Phase 10: Cloudflare CORS Worker - REMOVED (CORS fixed via NGINX)
  # The NGINX + Express.js environment variable solution provides sufficient CORS handling
  log_info "⏭️ Phase 10: Cloudflare CORS Worker (SKIPPED - NGINX solution sufficient)"

  log_info "🏗️ Phase 11: Enhanced CORS Validation and Service Health Check"
  create_phase_marker "phase-11-cors-validation" "started"

  # CRITICAL: Comprehensive CORS configuration validation
  log_info "🔍 CRITICAL: Performing comprehensive CORS configuration validation..."
  local cors_validation_script="${SCRIPT_DIR}/validate-cors-configuration.sh"

  # Make validation script executable
  if [[ -f "$cors_validation_script" ]]; then
    chmod +x "$cors_validation_script"

    log_info "Running comprehensive CORS validation..."
    if "$cors_validation_script" 2>&1 | tee -a "$LOG_FILE"; then
      log_success "✅ Comprehensive CORS validation passed"
      create_phase_marker "phase-11-cors-validation" "completed"
    else
      log_error "❌ Comprehensive CORS validation failed"
      create_phase_marker "phase-11-cors-validation" "failed"
      log_error "🚨 CRITICAL: Authentication may fail after VPS restart due to CORS issues"

      # Don't fail deployment but warn strongly
      log_warning "⚠️ Deployment continues but CORS issues need immediate attention"
      log_info "📋 Run the validation script manually: $cors_validation_script"
    fi
  else
    log_warning "⚠️ CORS validation script not found, using fallback test..."

    # Fallback to original test if new script not available
    local cors_test_script="${SCRIPT_DIR}/test-cors-fix.sh"
    if [[ -f "$cors_test_script" ]]; then
      chmod +x "$cors_test_script"

      if "$cors_test_script" "$PRODUCTION_DOMAIN" 2>&1 | tee -a "$LOG_FILE"; then
        log_success "✅ Fallback CORS validation passed"
        create_phase_marker "phase-11-cors-validation" "completed"
      else
        log_error "❌ Fallback CORS validation failed"
        create_phase_marker "phase-11-cors-validation" "failed"
        log_error "🚨 CRITICAL: Login functionality may be broken due to CORS issues"
      fi
    else
      log_warning "⚠️ No CORS test scripts found, skipping validation"
      create_phase_marker "phase-11-cors-validation" "skipped"
    fi
  fi

  log_info "🏗️ Phase 12: Intelligent Final Verification"
  # Initialize verification component
  init_intelligent_progress_tracking "final-verification" "essential"

  log_info "🔍 Performing intelligent service verification..."
  local services_ok=true

  # Check each service using intelligent service management
  log_info "Checking PostgreSQL (installed via intelligent modular script)..."
  if intelligent_service_management "postgresql" "status" "essential" 2; then
    log_success "✅ PostgreSQL is running"
  else
    log_warning "⚠️ PostgreSQL not running, attempting intelligent recovery..."
    if intelligent_service_management "postgresql" "start" "essential" 3; then
      log_success "✅ PostgreSQL recovered successfully"
    else
      log_warning "⚠️ PostgreSQL recovery failed, but deployment continues"
      services_ok=false
    fi
  fi

  log_info "Checking Nginx (installed via intelligent modular script)..."
  if intelligent_service_management "nginx" "status" "essential" 2; then
    log_success "✅ Nginx is running"
  else
    log_warning "⚠️ Nginx not running, attempting intelligent recovery..."
    if intelligent_service_management "nginx" "start" "essential" 3; then
      log_success "✅ Nginx recovered successfully"
    else
      log_warning "⚠️ Nginx recovery failed, but deployment continues"
      services_ok=false
    fi
  fi

  log_info "Checking PM2 (installed via intelligent modular script)..."
  if command -v pm2 >/dev/null 2>&1 && pm2 ping >/dev/null 2>&1; then
    log_success "✅ PM2 is running"
  else
    log_warning "⚠️ PM2 not running or not installed, but deployment continues"
    services_ok=false
  fi

  # Mark verification as completed
  update_deployment_progress "final-verification" "completed" "essential"

  # REMOVED: Phase 13 Service-Specific Optimization eliminated per user request
  # Phase 13 (11_service-optimization.sh) has been disabled to prevent NGINX configuration failures
  # The deployment now uses a simplified 13-phase system (Phases 0-12) for stability
  log_info "🏗️ Phase 13: Service-Specific Optimization (DISABLED - using stable configuration)"
  log_info "💡 Advanced service optimizations disabled to maintain system stability"

  # Mark deployment as completed
  update_deployment_progress "deployment-orchestrator" "completed" "essential"
  end_performance_timer "deployment-total"

  # Generate comprehensive performance report
  generate_performance_report

  # Update final deployment state
  python3 -c "
import json
with open('$DEPLOYMENT_STATE_FILE', 'r') as f:
    state = json.load(f)
state['phase'] = 'completed'
state['status'] = 'success'
with open('$DEPLOYMENT_STATE_FILE', 'w') as f:
    json.dump(state, f, indent=2)
" 2>/dev/null || true

  # Final intelligent deployment summary
  echo ""
  echo "=============================================================================="
  log_success "🎉 INTELLIGENT HAULING QR TRIP SYSTEM DEPLOYMENT COMPLETED SUCCESSFULLY!"
  echo "=============================================================================="
  log_info "📅 Deployment completed at: $(date)"
  log_info "🏗️ Architecture: Enhanced Intelligent (13 phases: 0-12, Phase 13 disabled)"
  log_info "🧠 Deployment system: Intelligent with optimization coordination & performance monitoring"
  log_info "🌐 Environment: $DEPLOYMENT_ENV"
  log_info "📁 Application directory: $APP_DIR"
  log_info "🗄️ Database: $DB_NAME"
  log_info "🌐 Server port: $SERVER_HTTP_PORT (Node.js v20.x LTS)"
  log_info "💻 Client port: $CLIENT_PORT"

  # Show intelligent deployment status
  echo ""
  show_deployment_status

  if [[ "$services_ok" == "true" ]]; then
    log_success "✅ All services are running correctly"
  else
    log_warning "⚠️ Some services may need manual attention"
  fi

  echo ""
  log_info "🔗 Access URLs:"
  if [[ -n "$DETECTED_VPS_IP" ]]; then
    log_info "   • Frontend: http://$DETECTED_VPS_IP:$CLIENT_PORT"
    log_info "   • Backend: http://$DETECTED_VPS_IP:$SERVER_HTTP_PORT"
    log_info "   • Health check: http://$DETECTED_VPS_IP:$SERVER_HTTP_PORT/health"
  else
    log_info "   • Frontend: http://localhost:$CLIENT_PORT"
    log_info "   • Backend: http://localhost:$SERVER_HTTP_PORT"
    log_info "   • Health check: http://localhost:$SERVER_HTTP_PORT/health"
  fi

  echo ""
  log_info "🔧 CORS Configuration Status:"
  log_info "   • NGINX CORS: ✅ Configured (primary solution)"
  log_info "   • Express.js CORS: ✅ Disabled via environment variables"
  log_info "   • Cloudflare Worker: ⏭️ Skipped (NGINX solution sufficient)"
  log_info "   • Test CORS: ./test-cors-fix.sh $PRODUCTION_DOMAIN"

  echo ""
  log_info "🧠 Intelligent Deployment Management:"
  log_info "   • Status: ./auto-deploy.sh status"
  log_info "   • Resume: ./auto-deploy.sh resume [component]"
  log_info "   • Reset: ./auto-deploy.sh reset [component]"
  log_info "   • Failed components: ./auto-deploy.sh failed"

  echo ""
  log_info "📋 Next Steps:"
  log_info "   1. Verify application is accessible via the URLs above"
  log_info "   2. Check service logs if any issues occur"
  log_info "   3. Monitor system performance and resource usage"
  log_info "   4. Use intelligent deployment commands for troubleshooting"

  echo ""

  # ENHANCED: Cleanup health monitoring
  cleanup_deployment_health

  log_success "🎉 Intelligent deployment completed successfully!"
  return 0
}

# =============================================================================
# INTELLIGENT DEPLOYMENT CLI INTEGRATION
# =============================================================================

# Check if this is a CLI command for intelligent deployment management
if [[ $# -gt 0 ]]; then
  case "$1" in
    "status"|"reset"|"resume"|"failed"|"completed"|"help"|"--help"|"-h")
      # Handle intelligent deployment CLI commands
      intelligent_deployment_cli "$@"
      exit $?
      ;;
    "--intelligent-status")
      show_deployment_status
      exit $?
      ;;
    "--intelligent-reset")
      reset_deployment_progress "${2:-all}"
      exit $?
      ;;
    "--intelligent-resume")
      resume_deployment "$2"
      exit $?
      ;;
  esac
fi

# Execute main deployment function
main "$@"
